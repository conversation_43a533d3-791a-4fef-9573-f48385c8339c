# Rayuela - Sistema de Recomendación

## Mejoras en la Sincronización de Planes y Límites

### Problema Resuelto

Se ha implementado una solución para el problema de sincronización entre los planes y límites definidos en el backend (`src/db/enums.py -> PLAN_LIMITS`) y el frontend (`src/lib/constants.ts -> SUBSCRIPTION_PLANS`). Anteriormente, esta información estaba duplicada y requería actualización manual en ambos lugares, lo que era propenso a errores.

### Solución Implementada

1. **Backend**:
   - Se ha mejorado el endpoint `/api/v1/usage/summary` para incluir información detallada sobre todos los planes disponibles.
   - Se ha creado un nuevo endpoint público `/api/v1/plans` que proporciona información completa sobre todos los planes disponibles.
   - Se ha centralizado la configuración de los IDs de precios de Stripe en `src/core/config.py`.

2. **Frontend**:
   - Se ha creado un nuevo hook `usePlans()` en `src/lib/usePlans.ts` que obtiene la información de planes desde el backend.
   - Se ha eliminado la duplicación de datos en `src/lib/constants.ts`, manteniendo solo la enumeración `SubscriptionPlan` por compatibilidad.
   - Se han actualizado todas las páginas que utilizaban la información de planes para usar el nuevo hook.

### Beneficios

- **Fuente única de verdad**: Toda la información de planes y límites ahora se define únicamente en el backend.
- **Sincronización automática**: El frontend obtiene automáticamente la información actualizada del backend.
- **Mantenimiento simplificado**: Los cambios en planes o límites solo necesitan hacerse en un lugar.
- **Mejor experiencia de usuario**: La información mostrada al usuario siempre está actualizada y es coherente.

### Archivos Modificados

#### Backend
- `src/api/v1/endpoints/usage_summary.py`: Mejorado para incluir información detallada de planes.
- `src/api/v1/endpoints/plans.py`: Nuevo endpoint para obtener información de planes.
- `src/core/config.py`: Centralización de IDs de precios de Stripe.
- `src/api/v1/api.py`: Registro del nuevo endpoint de planes.

#### Frontend
- `src/lib/usePlans.ts`: Nuevo hook para obtener y gestionar información de planes.
- `src/lib/api.ts`: Nueva función para llamar al endpoint de planes.
- `src/lib/constants.ts`: Simplificado, manteniendo solo la enumeración por compatibilidad.
- `src/app/(dashboard)/billing/page.tsx`: Actualizado para usar el nuevo hook.
- `src/app/(dashboard)/usage/page.tsx`: Actualizado para usar el nuevo hook.
- `src/app/(dashboard)/page.tsx`: Actualizado para usar el nuevo hook.

### Uso para Desarrolladores

Para acceder a la información de planes en componentes de React:

```tsx
import { usePlans } from '@/lib/usePlans';

function MyComponent() {
  const { 
    plans,              // Todos los planes como un objeto Record<string, PlanInfo>
    isLoading,          // Estado de carga
    error,              // Error si lo hay
    getPlanLimits,      // Función para obtener límites de un plan específico
    getPlanById,        // Función para obtener un plan específico
    getAllPlans,        // Función para obtener todos los planes como array
    getPlanName         // Función para obtener el nombre de un plan
  } = usePlans();
  
  // Ejemplo de uso
  if (isLoading) return <div>Cargando planes...</div>;
  if (error) return <div>Error al cargar planes</div>;
  
  return (
    <div>
      <h1>Plan actual: {plans && plans['PRO']?.name}</h1>
      <p>Límite de API calls: {plans && plans['PRO']?.limits.api_calls}</p>
      <p>Almacenamiento: {plans && plans['PRO']?.limits.storage_bytes} bytes</p>
    </div>
  );
}
```

Para obtener la información de planes directamente (fuera de componentes React):

```ts
import { getAvailablePlans } from '@/lib/api';

async function fetchPlans() {
  try {
    const plans = await getAvailablePlans();
    console.log('Planes disponibles:', plans);
    return plans;
  } catch (error) {
    console.error('Error al obtener planes:', error);
    return null;
  }
}
```
