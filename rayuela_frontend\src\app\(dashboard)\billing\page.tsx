"use client";

import React from 'react';
import AdminRoute from '@/components/auth/AdminRoute';

export default function BillingPage() {
    return React.createElement(AdminRoute, null,
        React.createElement('div', { className: "container mx-auto py-8" },
            React.createElement('h1', { className: "text-2xl font-bold mb-6" }, "Planes y Facturación"),
            React.createElement('p', { className: "text-gray-600 dark:text-gray-300" },
                "Página de facturación en desarrollo. Aquí podrás gestionar tu plan y métodos de pago."
            )
        )
    );
} 