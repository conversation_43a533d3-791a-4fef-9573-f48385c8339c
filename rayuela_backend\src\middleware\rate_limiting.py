# src/middleware/rate_limiting.py
from fastapi import Request, HTTPException, status
from fastapi.security import API<PERSON>ey<PERSON>eader
from redis.asyncio import Redis
import time
from typing import Optional, Dict, Any

from src.core.config import settings
from src.core.redis_utils import get_redis
from src.core.rate_limit_config import RateLimitConfig
from src.core.cache_manager import CacheManager
from src.utils.base_logger import log_info, log_error, log_warning

# Usar el mismo header que en deps.py
api_key_header = APIKeyHeader(name="X-API-Key", auto_error=False)


class RateLimiter:
    """Middleware optimizado para rate limiting basado en API keys."""

    def __init__(self):
        self.excluded_paths = [
            "/docs",
            "/openapi.json",
            "/redoc",
            "/favicon.ico",
            "/health",
        ]
        self.period = 60  # Segundos (periodo fijo para simplificar)
        self._rate_limit_config = RateLimitConfig()
        self._cache_manager = CacheManager()
        self._cache_prefix = "rate_limiter"

    def _get_limit_key(self, api_key: str) -> str:
        """Genera la clave para los límites en caché."""
        return f"{self._cache_prefix}:{api_key}:limit"

    async def __call__(self, request: Request, call_next):
        # Comprobar si la ruta debe ser excluida
        if any(request.url.path.startswith(p) for p in self.excluded_paths):
            return await call_next(request)

        try:
            # Obtener Redis
            redis: Redis = await get_redis()

            # Obtener API key del header
            api_key = request.headers.get("x-api-key")
            if not api_key:
                # Permitir acceso a endpoints públicos que no requieren API Key
                if request.url.path.startswith(
                    "/api/v1/"
                ) and not request.url.path.startswith("/api/v1/auth"):
                    log_warning(
                        f"Missing X-API-Key header for protected path: {request.url.path}"
                    )
                    raise HTTPException(
                        status_code=status.HTTP_401_UNAUTHORIZED,
                        detail="API Key is required",
                    )
                else:
                    # Ruta pública, no aplicar rate limiting
                    return await call_next(request)

            # Obtener el límite desde el servicio optimizado
            limit_key = self._get_limit_key(api_key)
            account_limit = await self._cache_manager.get(limit_key)
            
            if account_limit is None:
                # Si no está en caché, obtener de RateLimitConfig
                account_limit = await self._rate_limit_config.get_rate_limit(api_key)
                # Guardar en caché
                await self._cache_manager.set(limit_key, account_limit, ttl=60)  # 1 minuto

            if account_limit is None:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="Invalid or inactive API Key",
                )

            # Obtener el límite de peticiones por minuto
            requests_per_minute = account_limit.get(
                "requests_per_minute", settings.DEFAULT_RATE_LIMIT
            )

            # Generar clave para el contador de peticiones
            counter_key = f"rate_limit:{api_key}:{int(time.time() / self.period)}"

            # Incrementar contador
            current_count = await self._cache_manager.increment(counter_key)
            if current_count == 1:
                # Establecer TTL para el contador
                await self._cache_manager.set(counter_key, 1, ttl=self.period)

            # Verificar si se excedió el límite
            if current_count > requests_per_minute:
                raise HTTPException(
                    status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                    detail=f"Rate limit exceeded: {requests_per_minute} requests per minute",
                )

            # Continuar con la petición
            response = await call_next(request)
            return response

        except HTTPException:
            raise
        except Exception as e:
            log_error(f"Error in rate limiting middleware: {str(e)}")
            # En caso de error, permitir la petición
            return await call_next(request)
