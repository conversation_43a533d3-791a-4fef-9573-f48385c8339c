from enum import Enum


class Priority(str, Enum):
    LOW = "LOW"
    NORMAL = "NORMAL"
    HIGH = "HIGH"


class OrderDirection(str, Enum):
    ASC = "ASC"
    DESC = "DESC"


class NotificationType(str, Enum):
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    SUCCESS = "SUCCESS"


class InteractionType(str, Enum):
    VIEW = "VIEW"
    LIKE = "LIKE"
    PURCHASE = "PURCHASE"
    CART = "CART"
    RATING = "RATING"
    WISHLIST = "WISHLIST"
    CLICK = "CLICK"
    SEARCH = "SEARCH"
    FAVORITE = "FAVORITE"


class TrainingJobStatus(str, Enum):
    PENDING = "PENDING"
    RUNNING = "RUNNING"
    COMPLETED = "COMPLETED"
    FAILED = "FAILED"


class BatchIngestionJobStatus(str, Enum):
    PENDING = "PENDING"
    PROCESSING = "PROCESSING"
    COMPLETED = "COMPLETED"
    FAILED = "FAILED"
    CANCELED = "CANCELED"


class OrderStatus(str, Enum):
    PENDING = "PENDING"
    CONFIRMED = "CONFIRMED"
    PROCESSING = "PROCESSING"
    SHIPPED = "SHIPPED"
    DELIVERED = "DELIVERED"
    CANCELED = "CANCELED"
    REFUNDED = "REFUNDED"


class SubscriptionPlan(str, Enum):
    FREE = "FREE"
    STARTER = "STARTER"  # Renamed from BASIC
    PRO = "PRO"
    ENTERPRISE = "ENTERPRISE"


class RoleType(str, Enum):
    ADMIN = "ADMIN"
    EDITOR = "EDITOR"
    VIEWER = "VIEWER"


class PermissionType(str, Enum):
    # Basic permissions
    READ = "READ"
    WRITE = "WRITE"
    DELETE = "DELETE"
    ADMIN = "ADMIN"

    # Resource-specific permissions
    # Products
    PRODUCT_READ = "PRODUCT_READ"
    PRODUCT_CREATE = "PRODUCT_CREATE"
    PRODUCT_UPDATE = "PRODUCT_UPDATE"
    PRODUCT_DELETE = "PRODUCT_DELETE"

    # Users
    USER_READ = "USER_READ"
    USER_CREATE = "USER_CREATE"
    USER_UPDATE = "USER_UPDATE"
    USER_DELETE = "USER_DELETE"

    # System Users
    SYSTEM_USER_READ = "SYSTEM_USER_READ"
    SYSTEM_USER_CREATE = "SYSTEM_USER_CREATE"
    SYSTEM_USER_UPDATE = "SYSTEM_USER_UPDATE"
    SYSTEM_USER_DELETE = "SYSTEM_USER_DELETE"

    # Roles and Permissions
    ROLE_READ = "ROLE_READ"
    ROLE_CREATE = "ROLE_CREATE"
    ROLE_UPDATE = "ROLE_UPDATE"
    ROLE_DELETE = "ROLE_DELETE"
    PERMISSION_ASSIGN = "PERMISSION_ASSIGN"

    # Analytics
    ANALYTICS_READ = "ANALYTICS_READ"

    # Models and Training
    MODEL_READ = "MODEL_READ"
    MODEL_CREATE = "MODEL_CREATE"
    MODEL_UPDATE = "MODEL_UPDATE"
    MODEL_DELETE = "MODEL_DELETE"
    TRAINING_JOB_READ = "TRAINING_JOB_READ"
    TRAINING_JOB_CREATE = "TRAINING_JOB_CREATE"
    TRAINING_JOB_UPDATE = "TRAINING_JOB_UPDATE"
    TRAINING_JOB_CANCEL = "TRAINING_JOB_CANCEL"


# Basic permission sets
BASIC_READ_PERMISSIONS = {
    PermissionType.READ,
    PermissionType.PRODUCT_READ,
    PermissionType.USER_READ,
    PermissionType.MODEL_READ,
    PermissionType.TRAINING_JOB_READ,
}

BASIC_WRITE_PERMISSIONS = {
    PermissionType.WRITE,
    PermissionType.PRODUCT_CREATE,
    PermissionType.PRODUCT_UPDATE,
    PermissionType.USER_CREATE,
    PermissionType.USER_UPDATE,
    PermissionType.MODEL_CREATE,
    PermissionType.MODEL_UPDATE,
    PermissionType.TRAINING_JOB_CREATE,
    PermissionType.TRAINING_JOB_UPDATE,
}

BASIC_DELETE_PERMISSIONS = {
    PermissionType.DELETE,
    PermissionType.PRODUCT_DELETE,
    PermissionType.USER_DELETE,
    PermissionType.MODEL_DELETE,
    PermissionType.TRAINING_JOB_CANCEL,
}

ADMIN_PERMISSIONS = {
    PermissionType.ADMIN,
    PermissionType.SYSTEM_USER_READ,
    PermissionType.SYSTEM_USER_CREATE,
    PermissionType.SYSTEM_USER_UPDATE,
    PermissionType.SYSTEM_USER_DELETE,
    PermissionType.ROLE_READ,
    PermissionType.ROLE_CREATE,
    PermissionType.ROLE_UPDATE,
    PermissionType.ROLE_DELETE,
    PermissionType.PERMISSION_ASSIGN,
    PermissionType.ANALYTICS_READ,
}

PLAN_PERMISSIONS = {
    SubscriptionPlan.FREE: BASIC_READ_PERMISSIONS,
    SubscriptionPlan.STARTER: BASIC_READ_PERMISSIONS | BASIC_WRITE_PERMISSIONS,
    SubscriptionPlan.PRO: BASIC_READ_PERMISSIONS
    | BASIC_WRITE_PERMISSIONS
    | BASIC_DELETE_PERMISSIONS,
    SubscriptionPlan.ENTERPRISE: (
        BASIC_READ_PERMISSIONS
        | BASIC_WRITE_PERMISSIONS
        | BASIC_DELETE_PERMISSIONS
        | ADMIN_PERMISSIONS
    ),
}

PLAN_LIMITS = {
    SubscriptionPlan.FREE: {
        # Primary Metrics
        "api_calls_limit": 1000,  # 1K API calls per month
        "storage_limit": 10_000_000,  # 10MB storage
        # Rate Limiting
        "max_requests_per_minute": 60,  # 1 request per second
        "max_concurrent_requests": 5,  # 5 concurrent requests
        # Feature Differentiators
        "available_models": ["basic"],  # Only basic model
        "training_frequency": "manual",  # Manual training only
        "max_items": 1000,  # 1K catalog items
        "max_users": 1000,  # 1K end users
        "recommendation_cache_ttl": 3600,  # 1 hour cache
        "training_data_limit": 10000,  # 10K interactions for training
    },
    SubscriptionPlan.STARTER: {
        # Primary Metrics
        "api_calls_limit": 10000,  # 10K API calls per month
        "storage_limit": 100_000_000,  # 100MB storage
        # Rate Limiting
        "max_requests_per_minute": 300,  # 5 requests per second
        "max_concurrent_requests": 15,  # 15 concurrent requests
        # Feature Differentiators
        "available_models": ["basic", "standard"],  # Basic and standard models
        "training_frequency": "7 days",  # Weekly training
        "max_items": 10000,  # 10K catalog items
        "max_users": 10000,  # 10K end users
        "recommendation_cache_ttl": 1800,  # 30 minutes cache
        "training_data_limit": 100000,  # 100K interactions for training
    },
    SubscriptionPlan.PRO: {
        # Primary Metrics
        "api_calls_limit": 100000,  # 100K API calls per month
        "storage_limit": 1_000_000_000,  # 1GB storage
        # Rate Limiting
        "max_requests_per_minute": 1000,  # ~17 requests per second
        "max_concurrent_requests": 50,  # 50 concurrent requests
        # Feature Differentiators
        "available_models": ["basic", "standard", "advanced"],  # All models
        "training_frequency": "1 day",  # Daily training
        "max_items": 100000,  # 100K catalog items
        "max_users": 100000,  # 100K end users
        "recommendation_cache_ttl": 900,  # 15 minutes cache
        "training_data_limit": 1000000,  # 1M interactions for training
    },
    SubscriptionPlan.ENTERPRISE: {
        # Primary Metrics
        "api_calls_limit": 1000000,  # 1M API calls per month
        "storage_limit": 10_000_000_000,  # 10GB storage
        # Rate Limiting
        "max_requests_per_minute": 5000,  # ~83 requests per second
        "max_concurrent_requests": 200,  # 200 concurrent requests
        # Feature Differentiators
        "available_models": [
            "basic",
            "standard",
            "advanced",
            "custom",
        ],  # All models + custom
        "training_frequency": "6 hours",  # 4x daily training
        "max_items": -1,  # Unlimited catalog items
        "max_users": -1,  # Unlimited end users
        "recommendation_cache_ttl": 300,  # 5 minutes cache
        "training_data_limit": -1,  # Unlimited interactions for training
    },
}
