"""
Middleware for tracking API calls and enforcing subscription limits.
"""
from fastapi import Request, Response, HTTPException, status
from fastapi.security import API<PERSON>eyHeader
from starlette.middleware.base import BaseHTTPMiddleware
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update
from datetime import datetime, timezone, timedelta
import time
from typing import Optional, Dict, Any, Callable

from src.core.config import settings
from src.core.deps import get_db
from src.db.models import Subscription, Account
from src.utils.base_logger import log_info, log_error, log_warning

# Usar el mismo header que en deps.py
api_key_header = APIKeyHeader(name="X-API-Key", auto_error=False)


class ApiCallCounterMiddleware(BaseHTTPMiddleware):
    """Middleware for tracking API calls and enforcing subscription limits."""

    def __init__(self, app):
        super().__init__(app)
        self.excluded_paths = [
            "/docs",
            "/openapi.json",
            "/redoc",
            "/favicon.ico",
            "/health",
            "/api/v1/auth/token",  # Exclude auth endpoints
            "/api/v1/auth/register",
        ]
        self._cache = {}  # Cache en memoria para reducir llamadas a la BD
        self._cache_ttl = 60  # 60 segundos de TTL para la cache en memoria
        self._cache_timestamps = {}  # Timestamps para controlar TTL de la cache

    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """Process the request, count API calls, and enforce limits."""
        # Skip excluded paths
        if self._should_skip(request):
            return await call_next(request)

        # Get start time for performance tracking
        start_time = time.time()

        try:
            # Get API key from header
            api_key = request.headers.get("x-api-key")
            if not api_key:
                # Allow access to public endpoints that don't require API Key
                if request.url.path.startswith(
                    "/api/v1/"
                ) and not request.url.path.startswith("/api/v1/auth"):
                    log_warning(
                        f"Missing X-API-Key header for protected path: {request.url.path}"
                    )
                    raise HTTPException(
                        status_code=status.HTTP_401_UNAUTHORIZED,
                        detail="API Key is required",
                    )
                else:
                    # Public route, don't count API call
                    return await call_next(request)

            # Get DB session
            db = request.scope.get("db")
            if not db:
                # Create a new DB session if not available in request scope
                db_generator = get_db()
                db = await anext(db_generator)
                request.scope["db"] = db

            # Get account from API key
            account = await self._get_account_from_api_key(db, api_key)
            if not account:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="Invalid or inactive API Key",
                )

            # Get subscription for account
            subscription = await self._get_subscription(db, account.account_id)
            if not subscription or not subscription.is_active:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="No active subscription found",
                )

            # Check if we need to reset the monthly counter
            await self._check_and_reset_counter(db, subscription)

            # Check if the account has exceeded its API call limit
            if subscription.monthly_api_calls_used >= subscription.api_calls_limit:
                log_warning(
                    f"API call limit exceeded for account {account.account_id}: "
                    f"{subscription.monthly_api_calls_used}/{subscription.api_calls_limit}"
                )

                # Get the plan type and create a detailed error message
                detail = "Monthly API call limit exceeded"

                try:
                    # Try to get the plan type as a string
                    plan_type_str = str(subscription.plan_type)

                    # Create a more detailed error message for FREE plan users
                    if plan_type_str == "FREE":
                        detail = (
                            "Monthly API call limit exceeded. "
                            "You have reached the limit of your FREE plan. "
                            "Please upgrade to a paid plan to continue using the API."
                        )
                except Exception as e:
                    # If we can't get the plan type, use the default message
                    log_warning(f"Error getting plan type: {str(e)}")

                raise HTTPException(
                    status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                    detail=detail,
                )

            # Increment the API call counter
            await self._increment_api_call_counter(db, subscription)

            # Process the request
            response = await call_next(request)

            # Add rate limit headers to the response
            try:
                # Get the subscription data from the database again to ensure we have the latest values
                subscription_query = select(Subscription).where(
                    Subscription.account_id == account.account_id
                )
                subscription_result = await db.execute(subscription_query)
                fresh_subscription = subscription_result.scalars().first()

                if fresh_subscription:
                    # Add headers
                    response.headers["X-RateLimit-Limit"] = str(fresh_subscription.api_calls_limit)

                    # Calculate remaining calls
                    remaining = max(0, fresh_subscription.api_calls_limit - fresh_subscription.monthly_api_calls_used)
                    response.headers["X-RateLimit-Remaining"] = str(remaining)
            except Exception as e:
                # If we can't get the limits, don't add the headers
                log_warning(f"Error adding rate limit headers: {str(e)}")

            # Calculate reset date (first day of next month)
            today = datetime.now(timezone.utc)
            if today.month == 12:
                next_month = today.replace(year=today.year + 1, month=1, day=1)
            else:
                next_month = today.replace(month=today.month + 1, day=1)

            # Add reset date to headers
            response.headers["X-RateLimit-Reset"] = next_month.strftime("%Y-%m-%dT%H:%M:%SZ")

            # Log API call for analytics
            end_time = time.time()
            log_info(
                f"API call tracked",
                {
                    "account_id": account.account_id,
                    "path": request.url.path,
                    "method": request.method,
                    "response_time": end_time - start_time,
                    "status_code": response.status_code,
                },
            )

            return response

        except HTTPException as http_exc:
            # Re-raise HTTP exceptions
            raise http_exc
        except Exception as e:
            # Log and convert other exceptions to 500 errors
            log_error(f"Error in API call counter middleware: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Internal server error",
            )

    def _should_skip(self, request: Request) -> bool:
        """Check if the request should skip API call counting."""
        path = request.url.path

        # Skip excluded paths
        if any(path.startswith(excluded) for excluded in self.excluded_paths):
            return True

        # Skip OPTIONS requests (CORS preflight)
        if request.method == "OPTIONS":
            return True

        return False

    async def _get_account_from_api_key(
        self, db: AsyncSession, api_key: str
    ) -> Optional[Account]:
        """Get account from API key with caching and timing-safe comparison."""
        cache_key = f"account:{api_key}"

        # Check cache first
        if self._is_cache_valid(cache_key):
            return self._cache[cache_key]

        # Query database - get all active accounts and verify API key securely
        try:
            from src.core.security.api_key import verify_api_key

            # Get all active accounts (without API key filtering to prevent timing attacks)
            query = select(Account).where(
                Account.is_active == True,
                Account.deleted_at.is_(None),
                Account.api_key_hash.is_not(None),  # Only accounts with API keys
            )
            result = await db.execute(query)
            accounts = result.scalars().all()

            # Use timing-safe comparison to find matching account
            matching_account = None
            for account in accounts:
                if verify_api_key(api_key, account.api_key_hash):
                    matching_account = account
                    break

            # Update cache only if account found
            if matching_account:
                self._cache[cache_key] = matching_account
                self._cache_timestamps[cache_key] = time.time()

            return matching_account
        except Exception as e:
            log_error(f"Error getting account from API key: {str(e)}")
            return None

    async def _get_subscription(
        self, db: AsyncSession, account_id: int
    ) -> Optional[Subscription]:
        """Get subscription for account with caching."""
        cache_key = f"subscription:{account_id}"

        # Check cache first
        if self._is_cache_valid(cache_key):
            return self._cache[cache_key]

        # Query database
        try:
            query = select(Subscription).where(
                Subscription.account_id == account_id,
                Subscription.is_active == True,
            )
            result = await db.execute(query)
            subscription = result.scalars().first()

            # Update cache
            if subscription:
                self._cache[cache_key] = subscription
                self._cache_timestamps[cache_key] = time.time()

            return subscription
        except Exception as e:
            log_error(f"Error getting subscription: {str(e)}")
            return None

    async def _check_and_reset_counter(
        self, db: AsyncSession, subscription: Subscription
    ) -> None:
        """Check if the monthly counter needs to be reset and reset it if needed."""
        now = datetime.now(timezone.utc)

        # If last_reset_date is None or it's from a previous month, reset the counter
        if (
            subscription.last_reset_date is None
            or subscription.last_reset_date.month != now.month
            or subscription.last_reset_date.year != now.year
        ):
            try:
                # Update subscription in database
                stmt = (
                    update(Subscription)
                    .where(Subscription.account_id == subscription.account_id)
                    .values(
                        monthly_api_calls_used=0,
                        last_reset_date=now,
                    )
                )
                await db.execute(stmt)
                await db.commit()

                # We don't need to update the object in memory since we're invalidating the cache
                # The next time we get the subscription, it will have the updated values

                # Invalidate cache
                cache_key = f"subscription:{subscription.account_id}"
                self._cache.pop(cache_key, None)
                self._cache_timestamps.pop(cache_key, None)

                log_info(
                    f"Reset monthly API call counter for account {subscription.account_id}"
                )
            except Exception as e:
                log_error(f"Error resetting API call counter: {str(e)}")
                await db.rollback()

    async def _increment_api_call_counter(
        self, db: AsyncSession, subscription: Subscription
    ) -> None:
        """Increment the API call counter for the subscription."""
        try:
            # Update subscription in database
            stmt = (
                update(Subscription)
                .where(Subscription.account_id == subscription.account_id)
                .values(
                    monthly_api_calls_used=Subscription.monthly_api_calls_used + 1,
                )
            )
            await db.execute(stmt)
            await db.commit()

            # We don't need to update the object in memory since we're invalidating the cache
            # The next time we get the subscription, it will have the updated values

            # Invalidate cache
            cache_key = f"subscription:{subscription.account_id}"
            self._cache.pop(cache_key, None)
            self._cache_timestamps.pop(cache_key, None)
        except Exception as e:
            log_error(f"Error incrementing API call counter: {str(e)}")
            await db.rollback()

    def _is_cache_valid(self, key: str) -> bool:
        """Check if a cache entry is valid."""
        if key not in self._cache or key not in self._cache_timestamps:
            return False

        # Check if the cache entry has expired
        return time.time() - self._cache_timestamps[key] < self._cache_ttl
