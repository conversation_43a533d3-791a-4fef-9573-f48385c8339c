"""
Endpoints for database maintenance operations.
"""

from typing import Dict, Any, Optional
from fastapi import APIRouter, Depends, BackgroundTasks, HTTPException
from sqlalchemy.ext.asyncio import AsyncSession
from src.core.deps import get_db, get_current_admin_user
from src.db.models.system_user import SystemUser
from src.utils.maintenance import (
    cleanup_old_audit_logs,
    cleanup_old_interactions,
    cleanup_old_data_secure,
)
from src.utils.base_logger import log_info, log_error
from src.core.exceptions import handle_exceptions
from src.workers.celery_app import celery_app

router = APIRouter()


@router.post("/maintenance/cleanup-audit-logs", response_model=Dict[str, Any])
@handle_exceptions
async def cleanup_audit_logs(
    days_to_keep: int = 90,
    account_id: Optional[int] = None,
    run_async: bool = True,
    db: AsyncSession = Depends(get_db),
    current_admin: SystemUser = Depends(get_current_admin_user),
):
    """
    Limpia logs de auditoría más antiguos que el período especificado.

    Args:
        days_to_keep: Número de días a mantener los logs (por defecto 90)
        account_id: ID de la cuenta específica (None para todas las cuentas)
        run_async: Si es True, ejecuta la limpieza en segundo plano

    Returns:
        Diccionario con información sobre la operación o el ID de la tarea
    """
    try:
        account_msg = f" for account {account_id}" if account_id else ""

        if run_async:
            # Ejecutar como tarea de Celery
            task = celery_app.send_task(
                "cleanup_old_audit_logs",
                kwargs={
                    "days_to_keep": days_to_keep,
                    "account_id": account_id
                },
                queue="maintenance",
            )
            log_info(f"Scheduled audit logs cleanup task{account_msg}: {task.id}")
            return {
                "task_id": task.id,
                "status": "scheduled",
                "message": f"Cleanup of audit logs older than {days_to_keep} days{account_msg} scheduled",
            }
        else:
            # Ejecutar sincrónicamente
            result = await cleanup_old_audit_logs(days_to_keep, account_id=account_id)
            return result
    except Exception as e:
        log_error(f"Error cleaning up audit logs: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"Error cleaning up audit logs: {str(e)}"
        )


@router.post("/maintenance/cleanup-interactions", response_model=Dict[str, Any])
@handle_exceptions
async def cleanup_interactions(
    days_to_keep: int = 180,
    account_id: Optional[int] = None,
    batch_size: int = 10000,
    run_async: bool = True,
    db: AsyncSession = Depends(get_db),
    current_admin: SystemUser = Depends(get_current_admin_user),
):
    """
    Limpia interacciones más antiguas que el período especificado.

    Args:
        days_to_keep: Número de días a mantener las interacciones (por defecto 180)
        account_id: ID de la cuenta específica (None para todas las cuentas)
        batch_size: Tamaño del lote para eliminación (por defecto 10000)
        run_async: Si es True, ejecuta la limpieza en segundo plano

    Returns:
        Diccionario con información sobre la operación o el ID de la tarea
    """
    try:
        if run_async:
            # Ejecutar como tarea de Celery
            task = celery_app.send_task(
                "cleanup_old_interactions",
                kwargs={
                    "days_to_keep": days_to_keep,
                    "account_id": account_id,
                    "batch_size": batch_size,
                },
                queue="maintenance",
            )
            account_msg = f" for account {account_id}" if account_id else ""
            log_info(f"Scheduled interactions cleanup task{account_msg}: {task.id}")
            return {
                "task_id": task.id,
                "status": "scheduled",
                "message": f"Cleanup of interactions older than {days_to_keep} days{account_msg} scheduled",
            }
        else:
            # Ejecutar sincrónicamente
            result = await cleanup_old_interactions(
                days_to_keep=days_to_keep,
                account_id=account_id,
                batch_size=batch_size,
            )
            return result
    except Exception as e:
        log_error(f"Error cleaning up interactions: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"Error cleaning up interactions: {str(e)}"
        )


@router.get("/maintenance/task/{task_id}", response_model=Dict[str, Any])
@handle_exceptions
async def get_task_status(
    task_id: str,
    db: AsyncSession = Depends(get_db),
    current_admin: SystemUser = Depends(get_current_admin_user),
):
    """
    Obtiene el estado de una tarea de mantenimiento.

    Args:
        task_id: ID de la tarea

    Returns:
        Diccionario con información sobre la tarea
    """
    try:
        # Obtener el estado de la tarea de Celery
        task_result = celery_app.AsyncResult(task_id)

        result = {
            "task_id": task_id,
            "status": task_result.status,
        }

        # Si la tarea ha terminado, incluir el resultado
        if task_result.ready():
            if task_result.successful():
                result["result"] = task_result.result
            else:
                result["error"] = str(task_result.result)

        return result
    except Exception as e:
        log_error(f"Error getting task status: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"Error getting task status: {str(e)}"
        )


@router.post("/maintenance/cleanup-data-secure", response_model=Dict[str, Any])
@handle_exceptions
async def cleanup_data_secure_endpoint(
    days_to_keep: int = 180,
    account_id: Optional[int] = None,
    run_async: bool = True,
    db: AsyncSession = Depends(get_db),
    current_admin: SystemUser = Depends(get_current_admin_user),
):
    """
    Limpia datos antiguos de forma segura utilizando la función SECURITY DEFINER de PostgreSQL.

    Esta función proporciona una limpieza más segura con validación de parámetros y
    protección contra inyección SQL. Requiere un account_id específico.

    Args:
        days_to_keep: Número de días a mantener los datos (por defecto 180)
        account_id: ID de la cuenta (requerido)
        run_async: Si es True, ejecuta la limpieza en segundo plano

    Returns:
        Diccionario con información sobre la operación o el ID de la tarea
    """
    try:
        if account_id is None:
            raise HTTPException(
                status_code=400,
                detail="account_id is required for secure data cleanup"
            )

        if run_async:
            # Ejecutar como tarea de Celery
            task = celery_app.send_task(
                "cleanup_old_data_secure",
                kwargs={
                    "days_to_keep": days_to_keep,
                    "account_id": account_id
                },
                queue="maintenance",
            )
            log_info(f"Scheduled secure data cleanup task for account {account_id}: {task.id}")
            return {
                "task_id": task.id,
                "status": "scheduled",
                "message": f"Secure cleanup of data older than {days_to_keep} days for account {account_id} scheduled",
            }
        else:
            # Ejecutar sincrónicamente
            result = await cleanup_old_data_secure(
                days_to_keep=days_to_keep,
                account_id=account_id
            )
            return result
    except ValueError as e:
        log_error(f"Invalid parameters for secure data cleanup: {str(e)}")
        raise HTTPException(
            status_code=400, detail=f"Invalid parameters: {str(e)}"
        )
    except Exception as e:
        log_error(f"Error in secure data cleanup: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"Error in secure data cleanup: {str(e)}"
        )


@router.post("/maintenance/monitor-tables", response_model=Dict[str, Any])
@handle_exceptions
async def monitor_tables(
    run_async: bool = True,
    db: AsyncSession = Depends(get_db),
    current_admin: SystemUser = Depends(get_current_admin_user),
):
    """
    Monitorea las tablas de alto volumen y devuelve estadísticas.

    Args:
        run_async: Si es True, ejecuta el monitoreo en segundo plano

    Returns:
        Diccionario con estadísticas de las tablas o el ID de la tarea
    """
    try:
        if run_async:
            # Ejecutar como tarea de Celery
            task = celery_app.send_task(
                "monitor_high_volume_tables",
                queue="maintenance",
            )
            log_info(f"Scheduled table monitoring task: {task.id}")
            return {
                "task_id": task.id,
                "status": "scheduled",
                "message": "Table monitoring scheduled",
            }
        else:
            # Esta función aún no está implementada en el módulo de mantenimiento
            # En una implementación real, llamaríamos a una función como:
            # result = await monitor_high_volume_tables()

            # Por ahora, devolvemos un mensaje de placeholder
            return {
                "status": "not_implemented",
                "message": "Synchronous table monitoring not yet implemented",
                "tables": ["interactions", "audit_logs"],
            }
    except Exception as e:
        log_error(f"Error monitoring tables: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"Error monitoring tables: {str(e)}"
        )
